using Xunit;
using DTX.UI.Components;
using DTX.Song;
using DTX.Resources;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Moq;
using System;

namespace DTXMania.Test.UI
{
    /// <summary>
    /// Unit tests for Phase 2 Bar Generation and Rendering enhancements
    /// Tests the enhanced SongBarRenderer and bar information caching system
    /// </summary>
    public class Phase2BarRenderingTests : IDisposable
    {
        private readonly Mock<GraphicsDevice> _mockGraphicsDevice;
        private readonly Mock<IResourceManager> _mockResourceManager;
        private readonly SongBarRenderer _barRenderer;
        private readonly SongListNode _testSongNode;
        private readonly SongListNode _testBoxNode;

        public Phase2BarRenderingTests()
        {
            _mockGraphicsDevice = new Mock<GraphicsDevice>();
            _mockResourceManager = new Mock<IResourceManager>();
            _barRenderer = new SongBarRenderer(_mockGraphicsDevice.Object, _mockResourceManager.Object);

            // Create test song node
            _testSongNode = new SongListNode
            {
                Type = NodeType.Score,
                Title = "Test Song",
                Metadata = new SongMetadata
                {
                    Title = "Test Song",
                    Artist = "Test Artist",
                    FilePath = "test.dtx"
                },
                Scores = new SongScore[]
                {
                    new SongScore { BestScore = 95, BestRank = 85, FullCombo = true, PlayCount = 5 },
                    new SongScore { BestScore = 88, BestRank = 75, FullCombo = false, PlayCount = 3 },
                    null, // No score for difficulty 2
                    new SongScore { BestScore = 92, BestRank = 80, FullCombo = false, PlayCount = 2 },
                    null  // No score for difficulty 4
                }
            };

            // Create test box node
            _testBoxNode = new SongListNode
            {
                Type = NodeType.Box,
                Title = "Test Folder",
                Children = new System.Collections.Generic.List<SongListNode>()
            };
        }

        [Fact]
        public void GenerateBarInfo_WithValidSongNode_ShouldCreateCompleteBarInfo()
        {
            // Arrange
            int difficulty = 0;
            bool isSelected = true;

            // Act
            var barInfo = _barRenderer.GenerateBarInfo(_testSongNode, difficulty, isSelected);

            // Assert
            Assert.NotNull(barInfo);
            Assert.Equal(_testSongNode, barInfo.SongNode);
            Assert.Equal(BarType.Score, barInfo.BarType);
            Assert.Equal("Test Song", barInfo.TitleString);
            Assert.Equal(difficulty, barInfo.DifficultyLevel);
            Assert.Equal(isSelected, barInfo.IsSelected);
            Assert.Equal(Color.Yellow, barInfo.TextColor); // Selected text color
        }

        [Fact]
        public void GenerateBarInfo_WithBoxNode_ShouldCreateBoxBarInfo()
        {
            // Arrange
            int difficulty = 0;
            bool isSelected = false;

            // Act
            var barInfo = _barRenderer.GenerateBarInfo(_testBoxNode, difficulty, isSelected);

            // Assert
            Assert.NotNull(barInfo);
            Assert.Equal(_testBoxNode, barInfo.SongNode);
            Assert.Equal(BarType.Box, barInfo.BarType);
            Assert.Equal("[Test Folder]", barInfo.TitleString);
            Assert.Equal(difficulty, barInfo.DifficultyLevel);
            Assert.Equal(isSelected, barInfo.IsSelected);
            Assert.Equal(Color.Cyan, barInfo.TextColor); // Box node color
        }

        [Theory]
        [InlineData(NodeType.Score, BarType.Score)]
        [InlineData(NodeType.Box, BarType.Box)]
        [InlineData(NodeType.BackBox, BarType.Other)]
        [InlineData(NodeType.Random, BarType.Other)]
        public void GenerateBarInfo_WithDifferentNodeTypes_ShouldMapToCorrectBarType(NodeType nodeType, BarType expectedBarType)
        {
            // Arrange
            var node = new SongListNode { Type = nodeType, Title = "Test" };

            // Act
            var barInfo = _barRenderer.GenerateBarInfo(node, 0, false);

            // Assert
            Assert.NotNull(barInfo);
            Assert.Equal(expectedBarType, barInfo.BarType);
        }

        [Fact]
        public void UpdateBarInfo_WithDifficultyChange_ShouldUpdateClearLamp()
        {
            // Arrange
            var barInfo = _barRenderer.GenerateBarInfo(_testSongNode, 0, false);
            var originalClearLamp = barInfo.ClearLamp;

            // Act
            _barRenderer.UpdateBarInfo(barInfo, 1, false);

            // Assert
            Assert.Equal(1, barInfo.DifficultyLevel);
            // Clear lamp should be regenerated for new difficulty
            Assert.NotEqual(originalClearLamp, barInfo.ClearLamp);
        }

        [Fact]
        public void UpdateBarInfo_WithSelectionChange_ShouldUpdateTextColor()
        {
            // Arrange
            var barInfo = _barRenderer.GenerateBarInfo(_testSongNode, 0, false);
            var originalTextColor = barInfo.TextColor;

            // Act
            _barRenderer.UpdateBarInfo(barInfo, 0, true);

            // Assert
            Assert.True(barInfo.IsSelected);
            Assert.Equal(Color.Yellow, barInfo.TextColor);
            Assert.NotEqual(originalTextColor, barInfo.TextColor);
        }

        [Fact]
        public void GenerateBarInfo_WithNullNode_ShouldReturnNull()
        {
            // Act
            var barInfo = _barRenderer.GenerateBarInfo(null, 0, false);

            // Assert
            Assert.Null(barInfo);
        }

        [Theory]
        [InlineData(0, true, ClearStatus.FullCombo)]
        [InlineData(1, false, ClearStatus.Clear)]
        [InlineData(2, false, ClearStatus.NotPlayed)] // No score for difficulty 2
        [InlineData(3, false, ClearStatus.Clear)]
        [InlineData(4, false, ClearStatus.NotPlayed)] // No score for difficulty 4
        public void GenerateBarInfo_WithDifferentDifficulties_ShouldHaveCorrectClearStatus(int difficulty, bool expectedFullCombo, ClearStatus expectedStatus)
        {
            // Arrange & Act
            var barInfo = _barRenderer.GenerateBarInfo(_testSongNode, difficulty, false);

            // Assert
            Assert.NotNull(barInfo);
            Assert.Equal(difficulty, barInfo.DifficultyLevel);
            
            // Note: We can't directly test clear status since it's internal to the renderer,
            // but we can verify the bar info was created successfully for the difficulty
            if (difficulty < _testSongNode.Scores.Length && _testSongNode.Scores[difficulty] != null)
            {
                Assert.Equal(expectedFullCombo, _testSongNode.Scores[difficulty].FullCombo);
            }
        }

        [Fact]
        public void SongBarInfo_Dispose_ShouldDisposeAllTextures()
        {
            // Arrange
            var mockTitleTexture = new Mock<ITexture>();
            var mockPreviewTexture = new Mock<ITexture>();
            var mockClearLampTexture = new Mock<ITexture>();

            var barInfo = new SongBarInfo
            {
                TitleTexture = mockTitleTexture.Object,
                PreviewImage = mockPreviewTexture.Object,
                ClearLamp = mockClearLampTexture.Object
            };

            // Act
            barInfo.Dispose();

            // Assert
            mockTitleTexture.Verify(t => t.Dispose(), Times.Once);
            mockPreviewTexture.Verify(t => t.Dispose(), Times.Once);
            mockClearLampTexture.Verify(t => t.Dispose(), Times.Once);
        }

        [Theory]
        [InlineData(BarType.Score)]
        [InlineData(BarType.Box)]
        [InlineData(BarType.Other)]
        public void BarType_EnumValues_ShouldBeValid(BarType barType)
        {
            // This test ensures all BarType enum values are properly defined
            Assert.True(Enum.IsDefined(typeof(BarType), barType));
        }

        [Theory]
        [InlineData(ClearStatus.NotPlayed)]
        [InlineData(ClearStatus.Failed)]
        [InlineData(ClearStatus.Clear)]
        [InlineData(ClearStatus.FullCombo)]
        public void ClearStatus_EnumValues_ShouldBeValid(ClearStatus clearStatus)
        {
            // This test ensures all ClearStatus enum values are properly defined
            Assert.True(Enum.IsDefined(typeof(ClearStatus), clearStatus));
        }

        public void Dispose()
        {
            _barRenderer?.Dispose();
        }
    }
}
