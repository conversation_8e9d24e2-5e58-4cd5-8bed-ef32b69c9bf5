using Xunit;
using DTX.UI.Components;
using DTX.Song;
using DTX.Resources;
using Microsoft.Xna.Framework;
using Microsoft.Xna.Framework.Graphics;
using Moq;
using System;
using System.Collections.Generic;

namespace DTXMania.Test.UI
{
    /// <summary>
    /// Unit tests for Phase 2 SongListDisplay enhancements
    /// Tests the enhanced bar information caching and rendering system
    /// </summary>
    public class Phase2SongListDisplayTests : IDisposable
    {
        private readonly SongListDisplay _songListDisplay;
        private readonly Mock<GraphicsDevice> _mockGraphicsDevice;
        private readonly Mock<IResourceManager> _mockResourceManager;
        private readonly List<SongListNode> _testSongList;

        public Phase2SongListDisplayTests()
        {
            _songListDisplay = new SongListDisplay();
            _mockGraphicsDevice = new Mock<GraphicsDevice>();
            _mockResourceManager = new Mock<IResourceManager>();

            // Create test song list
            _testSongList = new List<SongListNode>
            {
                new SongListNode
                {
                    Type = NodeType.Score,
                    Title = "Song 1",
                    Metadata = new SongMetadata { Title = "Song 1", Artist = "Artist 1" },
                    Scores = new SongScore[] { new SongScore { BestScore = 95, FullCombo = true } }
                },
                new SongListNode
                {
                    Type = NodeType.Box,
                    Title = "Folder 1",
                    Children = new List<SongListNode>()
                },
                new SongListNode
                {
                    Type = NodeType.Score,
                    Title = "Song 2",
                    Metadata = new SongMetadata { Title = "Song 2", Artist = "Artist 2" },
                    Scores = new SongScore[] { new SongScore { BestScore = 88, FullCombo = false } }
                },
                new SongListNode
                {
                    Type = NodeType.Random,
                    Title = "Random Select"
                },
                new SongListNode
                {
                    Type = NodeType.BackBox,
                    Title = "Back"
                }
            };
        }

        [Fact]
        public void CurrentList_SetWithValidList_ShouldUpdateSuccessfully()
        {
            // Act
            _songListDisplay.CurrentList = _testSongList;

            // Assert
            Assert.Equal(_testSongList, _songListDisplay.CurrentList);
            Assert.Equal(0, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[0], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void RefreshDisplay_ShouldClearAllCaches()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            _songListDisplay.InitializeEnhancedRendering(_mockGraphicsDevice.Object, _mockResourceManager.Object);

            // Act
            _songListDisplay.RefreshDisplay();

            // Assert
            // Verify the method completes without throwing
            // In a real implementation, this would verify cache clearing
            Assert.NotNull(_songListDisplay);
        }

        [Fact]
        public void InitializeEnhancedRendering_WithValidParameters_ShouldSetupRendering()
        {
            // Act
            _songListDisplay.InitializeEnhancedRendering(_mockGraphicsDevice.Object, _mockResourceManager.Object);

            // Assert
            Assert.NotNull(_songListDisplay);
            // Verify initialization completes successfully
        }

        [Fact]
        public void SetEnhancedRendering_WithTrueValue_ShouldEnableEnhancedMode()
        {
            // Act
            _songListDisplay.SetEnhancedRendering(true);

            // Assert
            Assert.NotNull(_songListDisplay);
            // Verify the setting is applied without errors
        }

        [Fact]
        public void SetEnhancedRendering_WithFalseValue_ShouldDisableEnhancedMode()
        {
            // Act
            _songListDisplay.SetEnhancedRendering(false);

            // Assert
            Assert.NotNull(_songListDisplay);
            // Verify the setting is applied without errors
        }

        [Fact]
        public void MoveNext_WithValidList_ShouldAdvanceSelection()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            var initialIndex = _songListDisplay.SelectedIndex;

            // Act
            _songListDisplay.MoveNext();

            // Assert
            Assert.Equal(initialIndex + 1, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[1], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void MovePrevious_WithValidList_ShouldDecrementSelection()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            _songListDisplay.SelectedIndex = 2; // Start at index 2

            // Act
            _songListDisplay.MovePrevious();

            // Assert
            Assert.Equal(1, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[1], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void MoveNext_AtEndOfList_ShouldWrapToBeginning()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            _songListDisplay.SelectedIndex = _testSongList.Count - 1; // Last item

            // Act
            _songListDisplay.MoveNext();

            // Assert
            Assert.Equal(0, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[0], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void MovePrevious_AtBeginningOfList_ShouldWrapToEnd()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            _songListDisplay.SelectedIndex = 0; // First item

            // Act
            _songListDisplay.MovePrevious();

            // Assert
            Assert.Equal(_testSongList.Count - 1, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[_testSongList.Count - 1], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void CycleDifficulty_WithValidSong_ShouldChangeDifficulty()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            _songListDisplay.SelectedIndex = 0; // Select first song (has scores)
            var initialDifficulty = _songListDisplay.CurrentDifficulty;

            // Act
            _songListDisplay.CycleDifficulty();

            // Assert
            // Difficulty should change (may wrap around if only one difficulty available)
            Assert.NotNull(_songListDisplay.SelectedSong);
        }

        [Fact]
        public void ActivateSelected_WithValidSong_ShouldFireEvent()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            bool eventFired = false;
            SongActivatedEventArgs eventArgs = null;

            _songListDisplay.SongActivated += (sender, args) =>
            {
                eventFired = true;
                eventArgs = args;
            };

            // Act
            _songListDisplay.ActivateSelected();

            // Assert
            Assert.True(eventFired);
            Assert.NotNull(eventArgs);
            Assert.Equal(_testSongList[0], eventArgs.Song);
        }

        [Fact]
        public void CurrentDifficulty_SetWithValidValue_ShouldUpdateSuccessfully()
        {
            // Arrange
            int newDifficulty = 2;

            // Act
            _songListDisplay.CurrentDifficulty = newDifficulty;

            // Assert
            Assert.Equal(newDifficulty, _songListDisplay.CurrentDifficulty);
        }

        [Fact]
        public void CurrentDifficulty_SetWithNegativeValue_ShouldClampToZero()
        {
            // Act
            _songListDisplay.CurrentDifficulty = -1;

            // Assert
            Assert.Equal(0, _songListDisplay.CurrentDifficulty);
        }

        [Fact]
        public void CurrentDifficulty_SetWithHighValue_ShouldClampToMaximum()
        {
            // Act
            _songListDisplay.CurrentDifficulty = 10;

            // Assert
            Assert.Equal(4, _songListDisplay.CurrentDifficulty); // Max difficulty is 4
        }

        [Fact]
        public void IsScrolling_InitialState_ShouldBeFalse()
        {
            // Assert
            Assert.False(_songListDisplay.IsScrolling);
        }

        [Fact]
        public void SelectedIndex_SetWithValidValue_ShouldUpdateSelection()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;
            int newIndex = 2;

            // Act
            _songListDisplay.SelectedIndex = newIndex;

            // Assert
            Assert.Equal(newIndex, _songListDisplay.SelectedIndex);
            Assert.Equal(_testSongList[newIndex], _songListDisplay.SelectedSong);
        }

        [Fact]
        public void SelectedIndex_SetWithNegativeValue_ShouldClampToZero()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;

            // Act
            _songListDisplay.SelectedIndex = -1;

            // Assert
            Assert.Equal(0, _songListDisplay.SelectedIndex);
        }

        [Fact]
        public void SelectedIndex_SetWithHighValue_ShouldClampToMaximum()
        {
            // Arrange
            _songListDisplay.CurrentList = _testSongList;

            // Act
            _songListDisplay.SelectedIndex = 100;

            // Assert
            Assert.Equal(_testSongList.Count - 1, _songListDisplay.SelectedIndex);
        }

        [Fact]
        public void Dispose_ShouldCleanupResources()
        {
            // Arrange
            _songListDisplay.InitializeEnhancedRendering(_mockGraphicsDevice.Object, _mockResourceManager.Object);

            // Act & Assert - Should not throw
            _songListDisplay.Dispose();
        }

        [Fact]
        public void Constructor_ShouldInitializeWithDefaults()
        {
            // Arrange & Act
            var display = new SongListDisplay();

            // Assert
            Assert.NotNull(display.CurrentList);
            Assert.Empty(display.CurrentList);
            Assert.Equal(0, display.SelectedIndex);
            Assert.Equal(0, display.CurrentDifficulty);
            Assert.False(display.IsScrolling);
            Assert.Null(display.SelectedSong);
        }

        public void Dispose()
        {
            _songListDisplay?.Dispose();
        }
    }
}
